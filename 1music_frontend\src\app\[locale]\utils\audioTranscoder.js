import { FFmpeg } from '@ffmpeg/ffmpeg';
import { fetchFile, toBlobURL } from '@ffmpeg/util';
import { ImageProcessor } from './imageProcessor';

class AudioTranscoder {
    constructor() {
        this.ffmpeg = null;
        this.isLoaded = false;
        this.loadPromise = null;
    }

    async load() {
        // Return existing promise if already loading
        if (this.loadPromise) {
            return this.loadPromise;
        }

        // Return immediately if already loaded
        if (this.isLoaded) {
            return Promise.resolve();
        }

        // Create and cache the load promise
        this.loadPromise = this._loadFFmpeg();

        try {
            await this.loadPromise;
            this.isLoaded = true;
        } catch (error) {
            // Reset promise on failure so it can be retried
            this.loadPromise = null;
            throw error;
        }
    }

    async _loadFFmpeg() {
        this.ffmpeg = new FFmpeg();

        // Load FFmpeg with CDN URLs in parallel
        const baseURL = 'https://1music.cc/ffmpeg';

        const [coreURL, wasmURL] = await Promise.all([
            toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),
            toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm')
        ]);

        await this.ffmpeg.load({
            coreURL,
            wasmURL,
        });
    }

    async cropImageToSquare(imageFile) {
        // Use Canvas API for image processing - only crop WebP to square, others maintain aspect ratio
        return await ImageProcessor.processImage(imageFile, 500, 0.9);
    }

    async transcodeAudio(audioFile, coverImageFile, format, metadata = {}) {
        // Ensure FFmpeg is loaded
        await this.load();

        const inputAudioName = 'input_audio';
        const inputImageName = 'input_image.jpg';
        const outputName = `output.${format}`;

        try {
            // Prepare file operations in parallel
            const fileOperations = [
                this.ffmpeg.writeFile(inputAudioName, await fetchFile(audioFile))
            ];

            // Process cover image if provided (in parallel with audio file writing)
            let processedImageBlob = null;
            if (coverImageFile) {
                // Start image processing while audio file is being written
                const imageProcessPromise = this.cropImageToSquare(coverImageFile);

                // Wait for both audio file writing and image processing
                const [, processedImage] = await Promise.all([
                    fileOperations[0], // Audio file writing
                    imageProcessPromise
                ]);

                processedImageBlob = processedImage;
                // Write processed image to FFmpeg
                await this.ffmpeg.writeFile(inputImageName, await fetchFile(processedImageBlob));
            } else {
                // Just wait for audio file writing
                await fileOperations[0];
            }

            // Build FFmpeg command based on format
            let command = ['-i', inputAudioName];
            
            if (coverImageFile) {
                command.push('-i', inputImageName);
                command.push('-map', '0:a', '-map', '1');
            }

            if (format === 'mp3') {
                command.push(
                    '-codec:a', 'libmp3lame',
                    '-b:a', '320k'
                );
                if (coverImageFile) {
                    command.push(
                        '-c:v', 'mjpeg',
                        '-id3v2_version', '3',
                        '-metadata:s:v', 'title=Album cover',
                        '-metadata:s:v', 'comment=Cover (front)',
                        '-metadata:s:v', 'handler_name=Album cover'
                    );
                }
            } else if (format === 'flac') {
                command.push('-codec:a', 'flac');
                if (coverImageFile) {
                    command.push(
                        '-metadata:s:v', 'title=Album cover',
                        '-metadata:s:v', 'comment=Cover (front)',
                        '-disposition:v', 'attached_pic'
                    );
                }
            } else {
                throw new Error(`Unsupported format: ${format}`);
            }

            // Add metadata
            if (metadata.title) command.push('-metadata', `title=${metadata.title}`);
            if (metadata.artist) command.push('-metadata', `artist=${metadata.artist}`);
            if (metadata.album) command.push('-metadata', `album=${metadata.album}`);
            
            // Add custom metadata
            command.push(
                '-metadata', 'PURL=1music.cc',
                '-metadata', 'COMMENT=1music.cc'
            );

            command.push('-y', outputName);

            // Execute transcoding
            await this.ffmpeg.exec(command);

            // Read output file
            const data = await this.ffmpeg.readFile(outputName);
            
            // Clean up
            await this.ffmpeg.deleteFile(inputAudioName);
            if (coverImageFile) {
                await this.ffmpeg.deleteFile(inputImageName);
            }
            await this.ffmpeg.deleteFile(outputName);

            return new Uint8Array(data);

        } catch (error) {
            // Clean up on error
            try {
                await this.ffmpeg.deleteFile(inputAudioName);
                if (coverImageFile) {
                    await this.ffmpeg.deleteFile(inputImageName);
                }
                await this.ffmpeg.deleteFile(outputName);
            } catch (cleanupError) {
                // Ignore cleanup errors
            }
            throw error;
        }
    }

    setProgressCallback(callback) {
        if (this.ffmpeg) {
            this.ffmpeg.on('progress', callback);
        }
    }

    terminate() {
        if (this.ffmpeg) {
            try {
                this.ffmpeg.terminate();
            } catch (error) {
                console.warn('Error terminating FFmpeg:', error);
            }
        }

        // Reset all state
        this.ffmpeg = null;
        this.isLoaded = false;
        this.loadPromise = null;
    }
}

export default AudioTranscoder;
